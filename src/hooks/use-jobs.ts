import type { JobFilters } from '@/@types/job';
import { api } from '@/trpc/react';

export interface UseJobsOptions extends JobFilters {
  organizationId?: string;
  enabled?: boolean;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export interface UseJobsByRecruiterOptions {
  recruiterId?: string;
  page?: number;
  limit?: number;
  isActive?: boolean;
  sortBy?: 'title' | 'organizationName' | 'location' | 'expiryDate' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  enabled?: boolean;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export function useJobs(options: UseJobsOptions = {}) {
  const {
    page = 1,
    limit = 10,
    search,
    skills,
    location,
    organizationName,
    organizationId,
    isActive,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus = false,
  } = options;

  return api.job.getAll.useQuery(
    {
      page,
      limit,
      search,
      skills,
      location,
      organizationName,
      organizationId,
      isActive,
      sortBy,
      sortOrder,
    },
    {
      enabled,
      staleTime,
      refetchOnWindowFocus,
    }
  );
}

export function useJobsByRecruiter(options: UseJobsByRecruiterOptions = {}) {
  const {
    recruiterId,
    page = 1,
    limit = 10,
    isActive,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus = false,
  } = options;

  return api.job.getByRecruiter.useQuery(
    {
      recruiterId,
      page,
      limit,
      isActive,
      sortBy: sortBy === 'organizationName' ? 'companyName' : sortBy, // Temporary mapping
      sortOrder,
    },
    {
      enabled,
      staleTime,
      refetchOnWindowFocus,
    }
  );
}

export function useJob(id: string | undefined, enabled = true) {
  return api.job.getById.useQuery(
    { id: id! },
    {
      enabled: enabled && !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    }
  );
}

export function useJobStats(recruiterId?: string, enabled = true) {
  return api.job.getStats.useQuery(
    { recruiterId },
    {
      enabled,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    }
  );
}

// Mutations
export function useCreateJob() {
  const utils = api.useUtils();

  return api.job.create.useMutation({
    onSuccess: () => {
      // Invalidate and refetch job queries
      utils.job.getAll.invalidate();
      utils.job.getByRecruiter.invalidate();
      utils.job.getStats.invalidate();
    },
  });
}

export function useUpdateJob() {
  const utils = api.useUtils();

  return api.job.update.useMutation({
    onSuccess: (data) => {
      // Invalidate and refetch job queries
      utils.job.getAll.invalidate();
      utils.job.getByRecruiter.invalidate();
      utils.job.getById.invalidate({ id: data.id });
      utils.job.getStats.invalidate();
    },
  });
}
