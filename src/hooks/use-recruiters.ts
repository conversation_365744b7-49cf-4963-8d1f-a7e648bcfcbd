import type { RecruiterFilters } from '@/@types/recruiter';
import { api } from '@/trpc/react';

export interface UseRecruitersOptions extends RecruiterFilters {
  organizationId?: string;
  enabled?: boolean;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export function useRecruiters(options: UseRecruitersOptions = {}) {
  const {
    page = 1,
    limit = 10,
    search,
    organizationId,
    sortBy = 'name',
    sortOrder = 'asc',
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus = false,
  } = options;

  return api.recruiter.getAllRecruiters.useQuery(
    {
      page,
      limit,
      search,
      organizationId,
      sortBy,
      sortOrder,
    },
    {
      enabled,
      staleTime,
      refetchOnWindowFocus,
    }
  );
}

// Hook for creating recruiters
export function useCreateRecruiter() {
  const utils = api.useUtils();

  return api.recruiter.createRecruiter.useMutation({
    onSuccess: () => {
      // Invalidate and refetch the recruiters list
      utils.recruiter.getAllRecruiters.invalidate();
    },
  });
}

// Hook for getting a single recruiter by ID
export function useRecruiter(id: string, enabled = true) {
  return api.recruiter.getRecruiterById.useQuery(id, {
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating recruiters
export function useUpdateRecruiter() {
  const utils = api.useUtils();

  return api.recruiter.updateRecruiter.useMutation({
    onSuccess: () => {
      // Invalidate and refetch the recruiters list
      utils.recruiter.getAllRecruiters.invalidate();
    },
  });
}
