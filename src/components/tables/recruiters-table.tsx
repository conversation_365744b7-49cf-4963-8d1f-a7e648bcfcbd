'use client';

import { type ColumnDef } from '@tanstack/react-table';
import { CheckCircle, Edit, Eye, MoreHorizontal, Plus, User, XCircle } from 'lucide-react';

import type { Recruiter, RecruiterSortField } from '@/@types/recruiter';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SortableHeader, TableWrapper } from '@/components/ui/table-wrapper';
import { useRecruiters } from '@/hooks/use-recruiters';
import { useTableState } from '@/hooks/use-table-state';

interface RecruitersTableProps {
  organizationId?: string;
  showCreateButton?: boolean;
  className?: string;
  title?: string;
  description?: string;
  onCreateRecruiter?: () => void;
  onViewRecruiter?: (recruiter: Recruiter) => void;
  onEditRecruiter?: (recruiter: Recruiter) => void;
}

export function RecruitersTable({
  organizationId,
  showCreateButton = true,
  className,
  title = 'Recruiters',
  description = 'Manage team members and their roles',
  onCreateRecruiter,
  onViewRecruiter,
  onEditRecruiter,
}: RecruitersTableProps) {
  const { apiParams } = useTableState<RecruiterSortField>({
    defaultSortBy: 'name',
    defaultSortOrder: 'asc',
  });

  const { data, isLoading, error, refetch } = useRecruiters({
    ...apiParams,
    organizationId,
  });

  const recruiters = data?.data ?? [];
  const pagination = data?.pagination;

  // Default handlers
  const handleCreateRecruiter = () => {
    if (onCreateRecruiter) {
      onCreateRecruiter();
    } else {
      // Default behavior - could open a modal or navigate to create page
      console.log('Create recruiter clicked');
    }
  };

  const handleViewRecruiter = (recruiter: Recruiter) => {
    if (onViewRecruiter) {
      onViewRecruiter(recruiter);
    } else {
      // Default behavior - could open a details modal
      console.log('View recruiter:', recruiter.id);
    }
  };

  const handleEditRecruiter = (recruiter: Recruiter) => {
    if (onEditRecruiter) {
      onEditRecruiter(recruiter);
    } else {
      // Default behavior - could open an edit modal
      console.log('Edit recruiter:', recruiter.id);
    }
  };

  const columns: ColumnDef<Recruiter>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <SortableHeader column={column} className="min-w-0">
          Recruiter
        </SortableHeader>
      ),
      cell: ({ row }) => {
        const recruiter = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={recruiter.image} />
              <AvatarFallback>
                <User className="h-5 w-5" />
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <div className="font-medium truncate">{recruiter.name}</div>
              <div className="text-sm text-muted-foreground truncate">{recruiter.email}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'role',
      header: ({ column }) => <SortableHeader column={column}>Role</SortableHeader>,
      cell: ({ row }) => {
        const recruiter = row.original;
        return (
          <Badge variant={recruiter.role === 'admin' ? 'default' : 'secondary'}>
            {recruiter.role.charAt(0).toUpperCase() + recruiter.role.slice(1)}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'emailVerified',
      header: 'Status',
      cell: ({ row }) => {
        const recruiter = row.original;
        return (
          <div className="flex items-center space-x-2">
            {recruiter.emailVerified ? (
              <>
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-700">Verified</span>
              </>
            ) : (
              <>
                <XCircle className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-700">Unverified</span>
              </>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => <SortableHeader column={column}>Joined</SortableHeader>,
      cell: ({ row }) => {
        const recruiter = row.original;
        return (
          <div className="text-sm text-muted-foreground">
            {new Date(recruiter.createdAt).toLocaleDateString()}
          </div>
        );
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const recruiter = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleViewRecruiter(recruiter)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditRecruiter(recruiter)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Recruiter
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
          </div>
          {showCreateButton && (
            <Button onClick={handleCreateRecruiter}>
              <Plus className="mr-2 h-4 w-4" />
              Add Recruiter
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <TableWrapper
          columns={columns}
          data={recruiters}
          pagination={pagination}
          isLoading={isLoading}
          error={error}
          searchPlaceholder="Search recruiters..."
          onRetry={() => refetch()}
        />
      </CardContent>
    </Card>
  );
}
