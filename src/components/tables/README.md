# Reusable Table Components

This directory contains reusable table components that can be used throughout the application with organization filtering capabilities.

## Components

### ReusableJobsTable

A reusable jobs table component that supports organization filtering.

#### Props

- `organizationId?: string` - Filter jobs by organization ID
- `showCreateButton?: boolean` - Show/hide the create job button (default: true)
- `showFilters?: boolean` - Show/hide the status filter (default: true)
- `className?: string` - Additional CSS classes
- `title?: string` - Table title (default: "Jobs")
- `description?: string` - Table description
- `onCreateJob?: () => void` - Custom create job handler
- `onViewJob?: (job: Job) => void` - Custom view job handler
- `onManageJob?: (job: Job) => void` - Custom manage job handler
- `onViewApplications?: (job: Job) => void` - Custom view applications handler

#### Usage

```tsx
import { ReusableJobsTable } from '@/components/tables';

// Basic usage - shows all jobs
<ReusableJobsTable />

// Filter by organization
<ReusableJobsTable organizationId="org-123" />

// Custom handlers
<ReusableJobsTable
  organizationId="org-123"
  title="Organization Jobs"
  description="Jobs for this organization"
  onCreateJob={() => console.log('Create job')}
  onViewJob={(job) => console.log('View job:', job.id)}
/>
```

### ReusableRecruitersTable

A reusable recruiters table component that supports organization filtering.

#### Props

- `organizationId?: string` - Filter recruiters by organization membership
- `showCreateButton?: boolean` - Show/hide the create recruiter button (default: true)
- `className?: string` - Additional CSS classes
- `title?: string` - Table title (default: "Recruiters")
- `description?: string` - Table description
- `onCreateRecruiter?: () => void` - Custom create recruiter handler
- `onViewRecruiter?: (recruiter: Recruiter) => void` - Custom view recruiter handler
- `onEditRecruiter?: (recruiter: Recruiter) => void` - Custom edit recruiter handler

#### Usage

```tsx
import { ReusableRecruitersTable } from '@/components/tables';

// Basic usage - shows all recruiters
<ReusableRecruitersTable />

// Filter by organization
<ReusableRecruitersTable organizationId="org-123" />

// Custom handlers
<ReusableRecruitersTable
  organizationId="org-123"
  title="Team Members"
  description="Manage team members for this organization"
  onCreateRecruiter={() => console.log('Add member')}
  onViewRecruiter={(recruiter) => console.log('View recruiter:', recruiter.id)}
/>
```

## Features

- **Organization Filtering**: Both tables support filtering by organization ID
- **Pagination**: Built-in pagination support via TableWrapper
- **Search**: Built-in search functionality
- **Sorting**: Sortable columns
- **Responsive**: Mobile-friendly design
- **Customizable**: Flexible props for customization
- **Type Safe**: Full TypeScript support

## Backend Support

The tables rely on updated backend endpoints that support organization filtering:

- `api.job.getAll` - Now supports `organizationId` parameter
- `api.recruiter.getAllRecruiters` - Now supports `organizationId` parameter

The organization filtering for recruiters works by:
1. Finding all members of the organization via the Member schema
2. Filtering users with role 'recruiter' who are members of that organization

## Integration

These components are already integrated into:
- Organization details page (`/console/organizations/[id]`)
  - Members tab uses `ReusableRecruitersTable`
  - Jobs tab uses `ReusableJobsTable`

You can use them in other parts of the application where you need to display jobs or recruiters with optional organization filtering.
