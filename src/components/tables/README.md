# Simplified Table Components

This directory contains **two simple, reusable table components** that handle all job and recruiter display needs across the application.

## 🎯 **Simple Structure**

```
src/components/tables/
├── jobs-table.tsx          # ONE JobsTable for all job displays
├── recruiters-table.tsx    # ONE RecruitersTable for all recruiter displays
└── index.ts               # Clean exports
```

## 📋 **Components**

### JobsTable
**One table component for all job displays** - handles both main jobs page and organization-filtered views.

**Props:**
- `organizationId?: string` - Filter by organization (optional)
- `showModals?: boolean` - Enable built-in job details modal
- `title?: string` - Table title
- `description?: string` - Table description

**Usage:**
```tsx
import { JobsTable } from '@/components/tables';

// Main jobs page - shows all jobs with modals
<JobsTable 
  title="All Jobs"
  showModals={true}
/>

// Organization page - shows filtered jobs
<JobsTable 
  organizationId="org-123"
  title="Organization Jobs"
/>
```

### RecruitersTable
**One table component for all recruiter displays** - handles both main recruiters page and organization-filtered views.

**Props:**
- `organizationId?: string` - Filter by organization membership (optional)
- `title?: string` - Table title
- `description?: string` - Table description

**Usage:**
```tsx
import { RecruitersTable } from '@/components/tables';

// Main recruiters page - shows all recruiters
<RecruitersTable 
  title="All Recruiters"
/>

// Organization page - shows team members
<RecruitersTable 
  organizationId="org-123"
  title="Team Members"
/>
```

## 🚀 **Current Usage**

- **Main Jobs Page** (`/console/jobs`) → `<JobsTable showModals={true} />`
- **Main Recruiters Page** (`/console/recruiters`) → `<RecruitersTable />`
- **Organization Details** (`/console/organizations/[id]`) → Both tables with `organizationId` prop

## ✅ **Benefits of This Simple Structure**

1. **Just 2 files** instead of 6+ complex files
2. **One component per data type** - easy to understand
3. **Same component everywhere** - consistent behavior
4. **Organization filtering built-in** - just pass `organizationId`
5. **No wrapper components** - direct usage

## 🔧 **Features**

- ✅ Organization filtering
- ✅ Pagination, search, sorting
- ✅ Responsive design
- ✅ TypeScript support
- ✅ Built-in job details modal (when `showModals={true}`)
- ✅ Consistent UI across all pages

This simplified structure eliminates confusion and makes the codebase much easier to maintain!
