'use client';

import { type ColumnDef } from '@tanstack/react-table';
import { Building2, Calendar, Eye, MapPin, MoreHorizontal, Plus, Users } from 'lucide-react';
import { useState } from 'react';

import type { Job, JobSortField } from '@/@types/job';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { TableWrapper } from '@/components/ui/table-wrapper';
import { useJobs } from '@/hooks/use-jobs';
import { useTableState } from '@/hooks/use-table-state';
import { cn } from '@/lib/utils';

interface ReusableJobsTableProps {
  organizationId?: string;
  showCreateButton?: boolean;
  showFilters?: boolean;
  className?: string;
  title?: string;
  description?: string;
  onCreateJob?: () => void;
  onViewJob?: (job: Job) => void;
  onManageJob?: (job: Job) => void;
  onViewApplications?: (job: Job) => void;
}

// Helper function to safely get recruiter info
const getRecruiterInfo = (recruiter: unknown) => {
  if (typeof recruiter === 'string') {
    return { name: 'Unknown', email: 'Unknown' };
  }
  if (recruiter && typeof recruiter === 'object') {
    const recruiterObj = recruiter as { name?: string; email?: string; _id?: unknown };
    return {
      name: recruiterObj.name || 'Unknown',
      email: recruiterObj.email || 'Unknown',
    };
  }
  return { name: 'Unknown', email: 'Unknown' };
};

// Helper function to transform job data for components
const transformJobsForComponents = (jobs: unknown[]): Job[] => {
  return jobs.map((job) => {
    const jobObj = job as Record<string, unknown>;
    return {
      ...jobObj,
      organizationId:
        typeof jobObj.organizationId === 'string'
          ? jobObj.organizationId
          : jobObj.organizationId?.toString() || '',
      recruiter:
        typeof jobObj.recruiter === 'string'
          ? jobObj.recruiter
          : jobObj.recruiter?.toString() || '',
    } as unknown as Job;
  });
};

export function ReusableJobsTable({
  organizationId,
  showCreateButton = true,
  showFilters = true,
  className,
  title = 'Jobs',
  description = 'Manage job postings and track applications',
  onCreateJob,
  onViewJob,
  onManageJob,
  onViewApplications,
}: ReusableJobsTableProps) {
  const { apiParams } = useTableState<JobSortField>();
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);

  // Convert status filter to isActive for API compatibility
  const isActiveFilter =
    statusFilter === 'all'
      ? undefined
      : statusFilter === 'published'
        ? true
        : statusFilter === 'draft'
          ? false
          : undefined;

  // Use the jobs hook with organization filtering
  const {
    data: jobsData,
    isLoading,
    error,
    refetch,
  } = useJobs({
    ...apiParams,
    organizationId,
    isActive: isActiveFilter,
  });

  const jobs = transformJobsForComponents(jobsData?.data || []);

  // Default handlers
  const handleCreateJob = () => {
    if (onCreateJob) {
      onCreateJob();
    } else {
      // Default behavior - navigate to create job page
      window.location.href = '/console/jobs/create';
    }
  };

  const handleViewApplications = (job: Job) => {
    if (onViewApplications) {
      onViewApplications(job);
    } else {
      // Default behavior - navigate to applications page
      window.location.href = `/console/jobs/${job.id}/applications`;
    }
  };

  const handleManageJob = (job: Job) => {
    if (onManageJob) {
      onManageJob(job);
    } else {
      // Default behavior - navigate to job management page
      window.location.href = `/console/jobs/${job.id}?tab=basic`;
    }
  };

  const handleViewJob = (job: Job) => {
    if (onViewJob) {
      onViewJob(job);
    }
    // If no custom handler, we could show a default job details modal
  };

  const columns: ColumnDef<Job>[] = [
    {
      accessorKey: 'title',
      header: 'Job Title',
      cell: ({ row }) => {
        const job = row.original;
        const isExpired = new Date(job.expiryDate) < new Date();
        return (
          <div className="space-y-1">
            <div className="font-medium">{job.title}</div>
            <div className="text-sm text-muted-foreground">
              {job.organizationName || 'Unknown Organization'}
            </div>
            {isExpired && (
              <Badge variant="destructive" className="text-xs">
                Expired
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ row }) => {
        const job = row.original;
        return (
          <div className="flex items-center space-x-1">
            <MapPin className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{job.location}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'recruiter',
      header: 'Recruiter',
      cell: ({ row }) => {
        const job = row.original;
        const recruiterInfo = getRecruiterInfo(job.recruiter);
        return (
          <div className="text-sm">
            <div className="font-medium">{recruiterInfo.name}</div>
            <div className="text-muted-foreground">{recruiterInfo.email}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const job = row.original;
        const isExpired = new Date(job.expiryDate) < new Date();
        const status = isExpired ? 'expired' : job.isActive ? 'published' : 'draft';

        return (
          <Badge
            variant={
              status === 'published'
                ? 'default'
                : status === 'expired'
                  ? 'destructive'
                  : 'secondary'
            }
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'expiryDate',
      header: 'Expires',
      cell: ({ row }) => {
        const job = row.original;
        const expiryDate = new Date(job.expiryDate);
        const isExpired = expiryDate < new Date();

        return (
          <div
            className={cn('flex items-center space-x-1 text-sm', {
              'text-destructive': isExpired,
            })}
          >
            <Calendar className="h-3 w-3" />
            <span>{expiryDate.toLocaleDateString()}</span>
          </div>
        );
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const job = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onViewJob && (
                <DropdownMenuItem onClick={() => handleViewJob(job)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => handleManageJob(job)}>
                <Building2 className="mr-2 h-4 w-4" />
                Manage Job
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleViewApplications(job)}>
                <Users className="mr-2 h-4 w-4" />
                View Applications
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
          </div>
          {showCreateButton && (
            <Button onClick={handleCreateJob}>
              <Plus className="mr-2 h-4 w-4" />
              Add Job
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {showFilters && (
          <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Jobs</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <TableWrapper
          columns={columns}
          data={jobs}
          pagination={jobsData?.pagination}
          isLoading={isLoading}
          error={error}
          searchPlaceholder="Search jobs..."
          onRetry={() => refetch()}
        />
      </CardContent>
    </Card>
  );
}
