import { format } from 'date-fns';
import { Building2, CalendarIcon, Check, ChevronsUpDown, MapPin, X as XIcon } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import type { FormValues } from '@/@types/job';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { countries, getCountryLabel } from '@/data/countries';
import { currencies } from '@/data/currencies';
import { industries, skillsByIndustry } from '@/data/industries';
import { allSkills } from '@/data/skills';
import { useOrganizations } from '@/hooks/use-organizations';
import { cn } from '@/lib/utils';

import { jobStatuses } from './data/job-form-constants';

// Helper function to generate organization abbreviation
const getOrgAbbreviation = (name: string) => {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

interface BasicDetailsStepProps {
  form: UseFormReturn<FormValues>;
}

export function BasicDetailsStep({ form }: BasicDetailsStepProps) {
  const [orgSearchQuery, setOrgSearchQuery] = useState('');
  const [debouncedOrgSearch, setDebouncedOrgSearch] = useState('');

  // Industry change confirmation state
  const [showIndustryConfirmation, setShowIndustryConfirmation] = useState(false);
  const [pendingIndustryValue, setPendingIndustryValue] = useState<string>('');

  // Debounce the search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedOrgSearch(orgSearchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [orgSearchQuery]);

  // Use the API to fetch organizations with search
  const { data: organizationsData, isLoading: isLoadingOrganizations } = useOrganizations({
    search: debouncedOrgSearch,
    limit: 50,
  });

  // Function to check if form has values that would be reset
  const hasValuesToReset = () => {
    const skills = form.getValues('skills') || [];
    const questionsConfig = form.getValues('questionsConfig');

    const hasSkills = skills.length > 0;
    const hasQuestionTypes = questionsConfig?.questionTypes?.length > 0;
    const hasQuestions = (questionsConfig?.questions?.length || 0) > 0;

    return hasSkills || hasQuestionTypes || hasQuestions;
  };

  // Function to reset dependent fields
  const resetDependentFields = () => {
    form.setValue('skills', []);

    // Reset questions config
    const currentConfig = form.getValues('questionsConfig');
    form.setValue('questionsConfig', {
      ...currentConfig,
      questionTypes: [],
      questions: [],
      categoryConfigs: [],
    });
  };

  // Function to handle industry change with confirmation
  const handleIndustryChange = (newIndustry: string) => {
    const currentIndustry = form.getValues('industry');

    // If it's the same industry, no need for confirmation
    if (currentIndustry === newIndustry) {
      return;
    }

    // If there are values to reset and this is not the initial load, show confirmation
    if (currentIndustry && hasValuesToReset()) {
      setPendingIndustryValue(newIndustry);
      setShowIndustryConfirmation(true);
    } else {
      // No values to reset or initial load, proceed directly
      form.setValue('industry', newIndustry);
      resetDependentFields();
    }
  };

  // Function to confirm industry change
  const confirmIndustryChange = () => {
    form.setValue('industry', pendingIndustryValue);
    resetDependentFields();
    setShowIndustryConfirmation(false);
    setPendingIndustryValue('');
  };

  // Function to cancel industry change
  const cancelIndustryChange = () => {
    setShowIndustryConfirmation(false);
    setPendingIndustryValue('');
  };

  const organizations = useMemo(() => {
    return organizationsData?.data || [];
  }, [organizationsData]);
  return (
    <div className="space-y-6">
      {/* Job Title - Full Width */}
      <FormField
        control={form.control}
        name="title"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Job Title *</FormLabel>
            <FormControl>
              <Input placeholder="e.g. Senior Software Engineer" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Organization, Location, Expiry Date Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Organization Dropdown */}
        <FormField
          control={form.control}
          name="organizationId"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Organization *</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        'w-full justify-between',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      {field.value ? (
                        <div className="flex items-center gap-2">
                          <Avatar className="w-5 h-5">
                            <AvatarFallback className="text-xs font-medium">
                              {getOrgAbbreviation(
                                organizations.find((org) => org.id === field.value)?.name || ''
                              )}
                            </AvatarFallback>
                          </Avatar>
                          <span className="truncate">
                            {organizations.find((org) => org.id === field.value)?.name}
                          </span>
                        </div>
                      ) : (
                        'Select organization'
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <Command>
                    <CommandInput
                      placeholder="Search organizations..."
                      value={orgSearchQuery}
                      onValueChange={setOrgSearchQuery}
                    />
                    <CommandList>
                      <CommandEmpty>
                        {isLoadingOrganizations ? 'Loading...' : 'No organization found.'}
                      </CommandEmpty>
                      <CommandGroup>
                        <ScrollArea className="h-48">
                          {organizations?.map((organization) => (
                            <CommandItem
                              value={organization.name}
                              key={organization.id}
                              onSelect={() => {
                                form.setValue('organizationId', organization.id);
                                setOrgSearchQuery('');
                              }}
                              className="flex items-center gap-3 p-3"
                            >
                              <Check
                                className={cn(
                                  'h-4 w-4 shrink-0',
                                  organization.id === field.value ? 'opacity-100' : 'opacity-0'
                                )}
                              />
                              <div className="flex items-center gap-3 flex-1 min-w-0">
                                <Avatar className="w-8 h-8 shrink-0">
                                  <AvatarFallback className="text-sm font-medium">
                                    {getOrgAbbreviation(organization.name)}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex flex-col min-w-0 flex-1">
                                  <span className="font-medium text-foreground truncate">
                                    {organization.name}
                                  </span>
                                  <span className="text-sm text-muted-foreground truncate">
                                    @{organization.slug}
                                  </span>
                                </div>
                              </div>
                            </CommandItem>
                          ))}
                        </ScrollArea>
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Location */}
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Location *</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        'w-full justify-between',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      {field.value ? (
                        <div className="flex items-center">
                          <MapPin className="mr-2 h-4 w-4" />
                          {getCountryLabel(field.value)}
                        </div>
                      ) : (
                        'Select location'
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[300px] p-0" align="start">
                  <Command>
                    <CommandInput placeholder="Search location..." />
                    <CommandList>
                      <CommandEmpty>No location found.</CommandEmpty>
                      <CommandGroup>
                        <ScrollArea className="h-48">
                          {countries.map((country) => (
                            <CommandItem
                              value={country.label}
                              key={country.value}
                              onSelect={() => {
                                form.setValue('location', country.value);
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  country.value === field.value ? 'opacity-100' : 'opacity-0'
                                )}
                              />
                              <MapPin className="mr-2 h-4 w-4" />
                              {country.label}
                            </CommandItem>
                          ))}
                        </ScrollArea>
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Expiry Date */}
        <FormField
          control={form.control}
          name="expiryDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Expiry Date *</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {field.value ? format(field.value, 'PPP') : 'Pick a date'}
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Currency, Salary, Industry Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Currency */}
        <FormField
          control={form.control}
          name="currency"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Currency (Optional)</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      {currency.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Salary */}
        <FormField
          control={form.control}
          name="salary"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Salary (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g. 80,000 - 100,000" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Industry Selection */}
        <FormField
          control={form.control}
          name="industry"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Industry *</FormLabel>
              <Select onValueChange={handleIndustryChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry">
                      {field.value && (
                        <div className="flex items-center gap-2">
                          {(() => {
                            const selectedIndustry = industries.find(
                              (ind) => ind.value === field.value
                            );
                            const IconComponent = selectedIndustry?.icon || Building2;
                            return (
                              <>
                                <IconComponent className="w-4 h-4 text-muted-foreground" />
                                <span>{selectedIndustry?.label}</span>
                              </>
                            );
                          })()}
                        </div>
                      )}
                    </SelectValue>
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {industries.map((industry) => {
                    const IconComponent = industry.icon || Building2;
                    return (
                      <SelectItem
                        key={industry.value}
                        value={industry.value}
                        className="flex items-start gap-3 p-3"
                      >
                        <div className="flex items-start gap-3 w-full">
                          <IconComponent className="w-5 h-5 text-muted-foreground shrink-0 mt-0.5" />
                          <div className="flex flex-col min-w-0 flex-1">
                            <span className="font-medium text-foreground">{industry.label}</span>
                            {industry.description && (
                              <span className="text-sm text-muted-foreground">
                                {industry.description}
                              </span>
                            )}
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Skills and Status Row */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Skills Selection - 2/5 width */}
        <FormField
          control={form.control}
          name="skills"
          render={({ field }) => {
            const selectedIndustryValue = form.watch('industry');
            const availableSkills = selectedIndustryValue
              ? skillsByIndustry[selectedIndustryValue as keyof typeof skillsByIndustry] ||
                skillsByIndustry.other
              : skillsByIndustry.other;

            return (
              <FormItem className="flex flex-col md:col-span-2">
                <FormLabel>Required Skills *</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        role="combobox"
                        className={cn(
                          'w-full justify-between',
                          field.value.length === 0 && 'text-muted-foreground'
                        )}
                      >
                        {field.value.length > 0
                          ? `${field.value.length} skill${field.value.length > 1 ? 's' : ''} selected`
                          : 'Select skills'}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command>
                      <CommandInput placeholder="Search skills..." className="h-9" />
                      <CommandList>
                        <CommandEmpty>No skills found.</CommandEmpty>
                        <CommandGroup>
                          <ScrollArea className="h-48">
                            {availableSkills.map((skill) => (
                              <CommandItem
                                value={skill.label}
                                key={skill.value}
                                onSelect={() => {
                                  const currentSkills = field.value || [];
                                  if (currentSkills.includes(skill.value)) {
                                    // Remove skill
                                    field.onChange(currentSkills.filter((s) => s !== skill.value));
                                  } else {
                                    // Add skill
                                    field.onChange([...currentSkills, skill.value]);
                                  }
                                }}
                              >
                                <Check
                                  className={cn(
                                    'mr-2 h-4 w-4',
                                    field.value?.includes(skill.value) ? 'opacity-100' : 'opacity-0'
                                  )}
                                />
                                {skill.label}
                              </CommandItem>
                            ))}
                          </ScrollArea>
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {field.value.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {field.value.map((skillValue) => {
                      const skill = allSkills.find((s) => s.value === skillValue);
                      return (
                        <Badge key={skillValue} variant="secondary" className="text-xs">
                          {skill?.label || skillValue}
                          <button
                            type="button"
                            className="ml-1 hover:bg-muted-foreground/20 rounded-full"
                            onClick={() => {
                              field.onChange(field.value.filter((s) => s !== skillValue));
                            }}
                          >
                            <XIcon className="h-3 w-3" />
                          </button>
                        </Badge>
                      );
                    })}
                  </div>
                )}
                <FormMessage />
              </FormItem>
            );
          }}
        />

        {/* Job Status - 3/5 width */}
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="md:col-span-3">
              <FormLabel>Job Status</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status">
                      {field.value && (
                        <div className="flex items-center gap-2">
                          {(() => {
                            const selectedStatus = jobStatuses.find(
                              (status) => status.value === field.value
                            );
                            if (!selectedStatus) return null;
                            const IconComponent = selectedStatus.icon;
                            return (
                              <>
                                <div
                                  className={`p-1 rounded-sm ${selectedStatus.bgColor} ${selectedStatus.borderColor} border`}
                                >
                                  <IconComponent className={`w-3 h-3 ${selectedStatus.color}`} />
                                </div>
                                <span>{selectedStatus.label}</span>
                              </>
                            );
                          })()}
                        </div>
                      )}
                    </SelectValue>
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {jobStatuses.map((status) => {
                    const IconComponent = status.icon;
                    return (
                      <SelectItem
                        key={status.value}
                        value={status.value}
                        className="flex items-start gap-3 p-3"
                      >
                        <div className="flex items-start gap-3 w-full">
                          <div
                            className={`p-1.5 rounded-md ${status.bgColor} ${status.borderColor} border`}
                          >
                            <IconComponent className={`w-4 h-4 ${status.color}`} />
                          </div>
                          <div className="flex flex-col min-w-0 flex-1">
                            <span className="font-medium text-foreground">{status.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {status.description}
                            </span>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Industry Change Confirmation Dialog */}
      <AlertDialog open={showIndustryConfirmation} onOpenChange={setShowIndustryConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Industry?</AlertDialogTitle>
            <AlertDialogDescription>
              Changing the industry will reset the following:
              <ul className="mt-2 ml-4 list-disc space-y-1">
                <li>Selected skills</li>
                <li>Question categories</li>
                <li>Custom questions</li>
              </ul>
              Are you sure you want to continue?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelIndustryChange}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmIndustryChange}>
              Yes, Change Industry
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
