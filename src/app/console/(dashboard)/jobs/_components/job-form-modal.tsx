'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { type Resolver, useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { type FormValues, jobFormSchema as formSchema } from '@/@types/job';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { VisuallyHidden } from '@/components/ui/visually-hidden';
import { useCreateJob, useJob, useUpdateJob } from '@/hooks/use-jobs';
import { useOrganizations } from '@/hooks/use-organizations';

import { BasicDetailsStep } from './basic-details-step';
import { formSteps } from './data/job-form-constants';
import { DescriptionStep } from './description-step';
import { InterviewStep } from './interview-step';
import { QuestionsStep } from './questions-step';
import { ReviewStep } from './review-step';
import { StepProgress } from './step-progress';

interface JobFormModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  jobId?: string;
  mode: 'create' | 'edit';
}

export function JobFormModal({ open, onOpenChange, jobId, mode }: JobFormModalProps) {
  const [currentStep, setCurrentStep] = useState(1);

  // Hooks
  const createJobMutation = useCreateJob();
  const updateJobMutation = useUpdateJob();
  const { data: jobData, isLoading: isLoadingJob } = useJob(jobId, mode === 'edit' && open);
  const { data: organizationsData } = useOrganizations({
    limit: 100,
    enabled: open,
  });

  const isSubmitting = createJobMutation.isPending || updateJobMutation.isPending;

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as Resolver<FormValues>,
    defaultValues: {
      title: '',
      organizationId: '',
      industry: '',
      salary: '',
      currency: '',
      location: '',
      skills: [],
      status: 'draft' as const,
      description: '',
      requirements: '',
      benefits: '',
      interviewConfig: {
        duration: 30,
        instructions: '',
        difficultyLevel: 'normal',
        screenMonitoring: false,
        screenMonitoringMode: 'photo',
        screenMonitoringInterval: 30,
        cameraMonitoring: false,
        cameraMonitoringMode: 'photo',
        cameraMonitoringInterval: 30,
      },
      questionsConfig: {
        mode: 'manual' as const,
        totalQuestions: 5,
        categoryConfigs: [],
        questionTypes: [],
        questions: [],
      },
      expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    },
  });

  // Load job data for editing
  useEffect(() => {
    if (mode === 'edit' && jobData && open) {
      form.reset({
        title: jobData.title || '',
        organizationId: jobData.organizationId?.toString() || '',
        industry: jobData.industry || '',
        salary: jobData.salary || '',
        currency: jobData.currency || '',
        location: jobData.location || '',
        skills: jobData.skills || [],
        status: jobData.status || 'draft',
        description: jobData.description || '',
        requirements: jobData.requirements || '',
        benefits: jobData.benefits || '',
        interviewConfig: {
          duration: jobData.interviewConfig?.duration || 30,
          instructions: jobData.interviewConfig?.instructions || '',
          screenMonitoring: jobData.interviewConfig?.screenMonitoring || false,
          screenMonitoringMode: jobData.interviewConfig?.screenMonitoringMode || 'photo',
          screenMonitoringInterval: jobData.interviewConfig?.screenMonitoringInterval || 30,
          cameraMonitoring: jobData.interviewConfig?.cameraMonitoring || false,
          cameraMonitoringMode: jobData.interviewConfig?.cameraMonitoringMode || 'photo',
          cameraMonitoringInterval: jobData.interviewConfig?.cameraMonitoringInterval || 30,
        },
        questionsConfig: migrateQuestionsConfig(
          jobData.questionsConfig
        ) as FormValues['questionsConfig'],
        expiryDate: jobData.expiryDate
          ? new Date(jobData.expiryDate)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      });
    } else if (mode === 'create' && open) {
      form.reset({
        title: '',
        organizationId: '',
        industry: '',
        salary: '',
        currency: '',
        location: '',
        skills: [],
        status: 'draft',
        description: '',
        requirements: '',
        benefits: '',
        interviewConfig: {
          duration: 30,
          instructions: '',
          screenMonitoring: false,
          screenMonitoringMode: 'photo',
          screenMonitoringInterval: 30,
          cameraMonitoring: false,
          cameraMonitoringMode: 'photo',
          cameraMonitoringInterval: 30,
        },
        questionsConfig: {
          mode: 'manual',
          totalQuestions: 5,
          categoryConfigs: [],
          questionTypes: [],
          questions: [],
        },
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      });
      setCurrentStep(1);
    }
  }, [mode, jobData, open, form]);

  // Migration utility to convert old questionsConfig to new format
  const migrateQuestionsConfig = (questionsConfig: unknown) => {
    const config = questionsConfig as Record<string, unknown>;

    // If it already has the new structure, return as is
    if (config?.totalQuestions !== undefined && config?.categoryConfigs !== undefined) {
      return config;
    }

    // If it has the old structure, convert it
    if (config?.numberOfQuestions !== undefined) {
      const questionTypes = (config.questionTypes as string[]) || [];
      const questionsPerCategory =
        questionTypes.length > 0
          ? Math.ceil((config.numberOfQuestions as number) / questionTypes.length)
          : 1;

      const categoryConfigs = questionTypes.map((type: string) => ({
        type,
        numberOfQuestions: questionsPerCategory,
      }));

      return {
        mode: config.mode || 'manual',
        totalQuestions: config.numberOfQuestions || 5,
        categoryConfigs,
        questionTypes: config.questionTypes || [],
        questions: config.questions || [],
      };
    }

    // Default structure if nothing exists
    return {
      mode: 'manual',
      totalQuestions: 5,
      categoryConfigs: [],
      questionTypes: [],
      questions: [],
    };
  };

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      // Ensure questionsConfig exists
      if (!data.questionsConfig) {
        data.questionsConfig = {
          mode: 'manual',
          totalQuestions: 5,
          categoryConfigs: [],
          questionTypes: [],
          questions: [],
        };
      } else {
        // Migrate questionsConfig to new format if it has the old structure
        data.questionsConfig = migrateQuestionsConfig(
          data.questionsConfig
        ) as FormValues['questionsConfig'];
      }

      if (mode === 'edit' && jobId) {
        await updateJobMutation.mutateAsync({ id: jobId, ...data });
        toast.success('Job updated successfully');
      } else {
        // For create mode, we need to get the organization name from the selected organization
        const selectedOrg = organizationsData?.data.find((org) => org.id === data.organizationId);
        const dataWithOrgName = {
          ...data,
          organizationName: selectedOrg?.name || '',
        };
        await createJobMutation.mutateAsync(dataWithOrgName);
        toast.success('Job created successfully');
      }
      onOpenChange(false);
      form.reset();
      setCurrentStep(1);
    } catch (error) {
      console.error('Error submitting job:', error);
      // Error toast will be shown by the mutation
    }
  };

  // Navigation functions
  const nextStep = () => {
    if (currentStep < formSteps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Validate current step
  const validateStep = async (step: number) => {
    let fieldsToValidate: (keyof FormValues)[] = [];

    switch (step) {
      case 1:
        fieldsToValidate = [
          'title',
          'organizationId',
          'industry',
          'location',
          'skills',
          'expiryDate',
        ];
        break;
      case 2:
        fieldsToValidate = ['description'];
        break;
      case 3:
        fieldsToValidate = ['interviewConfig'];
        break;
      case 4:
        fieldsToValidate = ['questionsConfig'];

        // Special validation for step 4 to ensure questions configuration
        const questionsConfig = form.getValues('questionsConfig');

        // Validate based on the selected mode
        if (questionsConfig.mode === 'manual') {
          if (!questionsConfig.questions || questionsConfig.questions.length === 0) {
            toast.error('Please add some questions for the interview', {
              description:
                'Add at least one question using the manual entry form or AI suggestions',
            });
            return false;
          }
          // Check if manual mode has enough questions according to the configuration
          const totalRequired = questionsConfig.totalQuestions || 0;
          const totalProvided = questionsConfig.questions.length;
          if (totalProvided < totalRequired) {
            toast.error(`You need ${totalRequired} questions but only have ${totalProvided}`, {
              description: 'Please add more questions or reduce the required number in categories',
            });
            return false;
          }
        }

        // For ai-mode, just ensure question types are selected
        if (questionsConfig.mode === 'ai-mode' && questionsConfig.questionTypes.length === 0) {
          toast.error('Please select at least one question type', {
            description: 'AI will use these categories to generate questions during the interview',
          });
          return false;
        }
        break;
    }

    try {
      const result = await form.trigger(fieldsToValidate);
      return result;
    } catch {
      return false;
    }
  };

  const handleNext = async () => {
    const isValid = await validateStep(currentStep);
    if (isValid) {
      nextStep();
    }
  };

  // Watch questions config for debugging
  useEffect(() => {
    if (open) {
      const subscription = form.watch((value, { name }) => {
        if (name?.startsWith('questionsConfig')) {
          console.log(`Form field ${name} changed:`, value.questionsConfig);
        }
      });

      return () => subscription.unsubscribe();
    }
  }, [form, open]);

  if (isLoadingJob && mode === 'edit') {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <VisuallyHidden>
              <DialogTitle>Loading Job Data</DialogTitle>
            </VisuallyHidden>
          </DialogHeader>
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-2xl">
            {mode === 'create' ? 'Create New Job' : 'Edit Job'}
          </DialogTitle>
          <DialogDescription>
            Step {currentStep} of {formSteps.length}: {formSteps[currentStep - 1]?.description}
          </DialogDescription>
        </DialogHeader>

        {/* Step Progress */}
        <StepProgress currentStep={currentStep} steps={formSteps} />

        <div className="flex-1 overflow-y-auto px-1">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pb-4">
              {/* Step 1: Basic Details */}
              {currentStep === 1 && <BasicDetailsStep form={form} />}

              {/* Step 2: Description */}
              {currentStep === 2 && <DescriptionStep form={form} />}

              {/* Step 3: Interview Setup */}
              {currentStep === 3 && <InterviewStep form={form} />}

              {/* Step 4: Questions */}
              {currentStep === 4 && <QuestionsStep form={form} />}

              {/* Step 5: Review */}
              {currentStep === 5 && (
                <ReviewStep form={form} organizations={organizationsData?.data || []} />
              )}
            </form>
          </Form>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {currentStep > 1 && (
              <Button type="button" variant="outline" onClick={prevStep}>
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>

            {currentStep < formSteps.length ? (
              <Button type="button" onClick={handleNext}>
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button type="submit" onClick={form.handleSubmit(onSubmit)} disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {mode === 'create' ? 'Create Job' : 'Update Job'}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
