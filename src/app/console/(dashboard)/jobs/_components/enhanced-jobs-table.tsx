'use client';

import { useState } from 'react';

import type { Job } from '@/@types/job';
import { JobsTable } from '@/components/tables';

import { JobFormModal } from './job-form-modal';

interface EnhancedJobsTableProps {
  className?: string;
}

export function EnhancedJobsTable({ className }: EnhancedJobsTableProps) {
  // Modal states for the enhanced features
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [editingJobId, setEditingJobId] = useState<string | undefined>(undefined);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // Enhanced handlers
  const handleCreateJob = () => {
    setFormMode('create');
    setEditingJobId(undefined);
    setFormModalOpen(true);
  };

  const handleEditJob = (job: Job) => {
    setFormMode('edit');
    setEditingJobId(job.id);
    setFormModalOpen(true);
  };

  const handleViewApplications = (job: Job) => {
    // Navigate to the applications page
    window.location.href = `/console/jobs/${job.id}/applications`;
  };

  const handleManageJob = (job: Job) => {
    // Navigate to the job management/edit page
    window.location.href = `/console/jobs/${job.id}?tab=basic`;
  };

  return (
    <>
      <JobsTable
        className={className}
        title="All Jobs"
        description="Manage all job postings across your organization"
        onCreateJob={handleCreateJob}
        onManageJob={handleManageJob}
        onViewApplications={handleViewApplications}
        showFilters={true}
      />

      {/* Enhanced Modal Features */}
      <JobFormModal
        open={formModalOpen}
        onOpenChange={setFormModalOpen}
        jobId={editingJobId}
        mode={formMode}
      />
    </>
  );
}
