'use client';

import {
  ArrowLeft,
  Briefcase,
  Building2,
  Calendar,
  Edit,
  Globe,
  Loader2,
  Users,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { JobsTable, RecruitersTable } from '@/components/tables';
import { useOrganization } from '@/hooks/use-organizations';

import { AddOrganizationForm } from '../../_components/add-organization-form';

interface OrganizationDetailsClientProps {
  organizationId: string;
}

export function OrganizationDetailsClient({ organizationId }: OrganizationDetailsClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'overview');

  const { data: organization, isLoading, error } = useOrganization(organizationId);

  // Update URL when tab changes
  const handleTabChange = useCallback(
    (tab: string) => {
      setActiveTab(tab);
      const url = new URL(window.location.href);
      url.searchParams.set('tab', tab);
      router.replace(url.pathname + url.search, { scroll: false });
    },
    [router]
  );

  // Update active tab when URL changes
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    if (tabFromUrl && ['overview', 'members', 'jobs'].includes(tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams]);

  if (isLoading) {
    return (
      <div className="flex h-48 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !organization) {
    return (
      <div className="flex h-48 items-center justify-center">
        <div className="text-center">
          <p className="text-lg font-medium">Organization not found</p>
          <Button
            variant="outline"
            onClick={() => router.push('/console/organizations')}
            className="mt-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Organizations
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Combined Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push('/console/organizations')}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-muted/50">
              {organization.logo ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={organization.logo}
                  alt={organization.name}
                  className="h-12 w-12 rounded object-cover"
                />
              ) : (
                <Building2 className="h-8 w-8 text-muted-foreground" />
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold tracking-tight">{organization.name}</h1>
                <Badge variant="secondary">{organization.slug}</Badge>
              </div>
              <p className="text-muted-foreground">
                Created {new Date(organization.createdAt).toLocaleDateString()}
                {organization.description && ` • ${organization.description}`}
              </p>
            </div>
            <AddOrganizationForm
              organization={organization}
              trigger={
                <Button variant="outline">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Organization
                </Button>
              }
            />
          </div>
        </CardHeader>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="jobs">Jobs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Members</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">-</div>
                <p className="text-xs text-muted-foreground">Active team members</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
                <Briefcase className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">-</div>
                <p className="text-xs text-muted-foreground">Open positions</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">-</div>
                <p className="text-xs text-muted-foreground">Across all jobs</p>
              </CardContent>
            </Card>
          </div>

          {/* Organization Information */}
          <Card>
            <CardHeader>
              <CardTitle>Organization Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-3">
                <div className="flex items-center gap-2 text-sm">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Organization ID:</span>
                  <span className="text-muted-foreground font-mono">{organization.id}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Slug:</span>
                  <span className="text-muted-foreground font-mono">{organization.slug}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Created:</span>
                  <span className="text-muted-foreground">
                    {new Date(organization.createdAt).toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Last Updated:</span>
                  <span className="text-muted-foreground">
                    {new Date(organization.updatedAt).toLocaleString()}
                  </span>
                </div>
                {organization.metadata && (
                  <div className="flex items-start gap-2 text-sm">
                    <Globe className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <span className="font-medium">Metadata:</span>
                    <span className="text-muted-foreground">{organization.metadata}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members" className="space-y-6">
          <RecruitersTable
            organizationId={organizationId}
            title="Team Members"
            description={`Manage team members and their roles within ${organization.name}`}
            onCreateRecruiter={() => {
              // Handle add member action
              console.log('Add member clicked');
            }}
          />
        </TabsContent>

        <TabsContent value="jobs" className="space-y-6">
          <JobsTable
            organizationId={organizationId}
            title="Job Postings"
            description={`Manage job postings and track applications for ${organization.name}`}
            onCreateJob={() => {
              // Handle create job action
              window.location.href = '/console/jobs/create';
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
