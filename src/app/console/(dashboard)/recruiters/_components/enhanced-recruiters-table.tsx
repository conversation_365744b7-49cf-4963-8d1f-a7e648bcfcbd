'use client';

import { useState } from 'react';

import type { Recruiter } from '@/@types/recruiter';
import { RecruitersTable } from '@/components/tables';

import { AddRecruiterForm } from './add-recruiter-form';
import { RecruiterDetailsSheet } from './recruiter-details-sheet';

interface EnhancedRecruitersTableProps {
  className?: string;
}

export function EnhancedRecruitersTable({ className }: EnhancedRecruitersTableProps) {
  // Modal states for the enhanced features
  const [addRecruiterOpen, setAddRecruiterOpen] = useState(false);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [selectedRecruiter, setSelectedRecruiter] = useState<Recruiter | null>(null);

  // Enhanced handlers
  const handleCreateRecruiter = () => {
    setAddRecruiterOpen(true);
  };

  const handleViewRecruiter = (recruiter: Recruiter) => {
    setSelectedRecruiter(recruiter);
    setDetailsOpen(true);
  };

  const handleEditRecruiter = (recruiter: Recruiter) => {
    setSelectedRecruiter(recruiter);
    setEditOpen(true);
  };

  return (
    <>
      <RecruitersTable
        className={className}
        title="All Recruiters"
        description="Manage recruiters and team members across your organization"
        onCreateRecruiter={handleCreateRecruiter}
        onViewRecruiter={handleViewRecruiter}
        onEditRecruiter={handleEditRecruiter}
      />

      {/* Enhanced Modal Features */}
      <AddRecruiterForm open={addRecruiterOpen} onOpenChange={setAddRecruiterOpen} />

      {selectedRecruiter && (
        <>
          <RecruiterDetailsSheet
            recruiterId={selectedRecruiter.id}
            open={detailsOpen}
            onOpenChange={setDetailsOpen}
            onEdit={() => {
              setDetailsOpen(false);
              setEditOpen(true);
            }}
          />

          <AddRecruiterForm
            recruiter={selectedRecruiter}
            open={editOpen}
            onOpenChange={setEditOpen}
          />
        </>
      )}
    </>
  );
}
