'use client';

import { RecruiterLoginForm } from './recruiter-login-form';

export function RecruiterLoginWrapper() {
  const handleEmailSubmit = async (email: string) => {
    // Implement email OTP sending logic for recruiters
    console.log(`Sending OTP to recruiter email: ${email}`);
    // Call API to send <PERSON>TP to recruiter
    // This could include additional validation for recruiter email domains
  };

  const handleOtpSubmit = async (otp: string) => {
    // Implement OTP verification logic for recruiters
    console.log(`Verifying recruiter OTP: ${otp}`);
    // Call API to verify OTP and login recruiter
    // Redirect to recruiter dashboard on success
  };

  return <RecruiterLoginForm onEmailSubmit={handleEmailSubmit} onOtpSubmit={handleOtpSubmit} />;
}
