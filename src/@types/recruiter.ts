import { z } from 'zod';

import type { PaginationMeta } from './common';

// Database interface types
export interface Recruiter {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image: string;
  role: 'user' | 'admin' | 'recruiter';
  createdAt: Date;
  updatedAt: Date;
}

export interface PaginatedRecruiters {
  data: Recruiter[];
  pagination: PaginationMeta;
}

export interface RecruiterFilters {
  page?: number;
  limit?: number;
  search?: string;
  organizationId?: string;
  sortBy?: 'name' | 'email' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export type RecruiterSortField = 'name' | 'email' | 'createdAt' | 'updatedAt';

// Recruiter creation schema
export const createRecruiterSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  emailVerified: z.boolean().optional().default(false),
  image: z.string().url().optional(),
});

// Get all recruiters schema
export const getAllRecruitersSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  organizationId: z.string().optional(),
  sortBy: z.enum(['name', 'email', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Get recruiter by ID schema
export const getRecruiterByIdSchema = z.string().min(24).max(24);

// Get recruiter by email schema
export const getRecruiterByEmailSchema = z.string().email();

// Update recruiter schema
export const updateRecruiterSchema = z.object({
  id: z.string().min(24).max(24),
  name: z.string().min(2).max(100).optional(),
  email: z.string().email().optional(),
  emailVerified: z.boolean().optional(),
  image: z.string().url().optional(),
});

// Delete recruiter schema
export const deleteRecruiterSchema = z.string().min(24).max(24);

// Form validation schema for adding/editing recruiters
export const addRecruiterSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  emailVerified: z.boolean(),
});

// Type exports for TypeScript inference
export type CreateRecruiterInput = z.infer<typeof createRecruiterSchema>;
export type GetAllRecruitersInput = z.infer<typeof getAllRecruitersSchema>;
export type GetRecruiterByIdInput = z.infer<typeof getRecruiterByIdSchema>;
export type GetRecruiterByEmailInput = z.infer<typeof getRecruiterByEmailSchema>;
export type UpdateRecruiterInput = z.infer<typeof updateRecruiterSchema>;
export type DeleteRecruiterInput = z.infer<typeof deleteRecruiterSchema>;
export type AddRecruiterFormData = z.infer<typeof addRecruiterSchema>;
