import type { Document } from 'mongoose';
import mongoose from 'mongoose';
import { z } from 'zod';

import type { PaginationMeta } from './common';
import type { IOrganization } from './organization';
import type { IUser } from './user';

// Helper function to validate MongoDB ObjectId
const objectIdSchema = z.string().refine(
  (val) => {
    return mongoose.Types.ObjectId.isValid(val);
  },
  {
    message: 'Invalid ObjectId format',
  }
);

// Database interface types
export interface IQuestion {
  id: string;
  type: string;
  question: string;
  isAIGenerated?: boolean;
}

export interface IQuestionCategoryConfig {
  type: string; // e.g., 'technical-coding', 'behavioral', etc.
  numberOfQuestions: number; // Number of questions for this specific category
}

export interface IQuestionConfig {
  mode: 'manual' | 'ai-mode'; // manual questions with AI help, or full AI mode
  totalQuestions: number; // Total number of questions across all categories
  categoryConfigs: IQuestionCategoryConfig[]; // Configuration per category
  questionTypes: string[]; // Selected question types (for backward compatibility)
  questions?: IQuestion[]; // For manual mode (optional for ai-mode)
}

export interface IInterviewConfig {
  duration: number; // Interview duration in minutes
  instructions?: string; // Interview instructions
  difficultyLevel: 'easy' | 'normal' | 'hard' | 'expert' | 'advanced'; // Interview difficulty level
  screenMonitoring: boolean; // Enable screen monitoring
  screenMonitoringMode: 'photo' | 'video'; // Screen monitoring mode
  screenMonitoringInterval?: 30 | 60; // Screen monitoring interval in seconds (if mode is photo/video)
  cameraMonitoring: boolean; // Enable camera monitoring
  cameraMonitoringMode: 'photo' | 'video'; // Camera monitoring mode
  cameraMonitoringInterval?: 30 | 60; // Camera monitoring interval in seconds
}

export interface IJob extends Document {
  id: string;
  title: string;
  description: string;
  organizationId: mongoose.Types.ObjectId | IOrganization; // Changed to ObjectId reference
  organizationName?: string; // Keep for backward compatibility
  industry: string; // New field
  expiryDate: Date;
  location: string;
  salary?: string;
  currency?: string;
  skills: string[];
  requirements?: string;
  benefits?: string;
  recruiter: mongoose.Types.ObjectId | IUser;
  isActive: boolean;
  status: 'draft' | 'published' | 'expired' | 'deleted'; // Added deleted status
  interviewConfig: IInterviewConfig; // New interview configuration object
  questionsConfig: IQuestionConfig; // New unified questions configuration
  deletedAt?: Date; // When the job was deleted
  deletedBy?: string; // Who deleted the job
  createdAt: Date;
  updatedAt: Date;
}

// Question schema
export const questionSchema = z.object({
  id: z.string(),
  type: z.string(),
  question: z.string().min(1, 'Question is required'),
  isAIGenerated: z.boolean().optional(),
});

// Question category config schema
export const questionCategoryConfigSchema = z.object({
  type: z.string(),
  numberOfQuestions: z
    .number()
    .min(1, 'At least 1 question required per category')
    .max(20, 'Maximum 20 questions per category'),
});

// Questions config schema
export const questionsConfigSchema = z.object({
  mode: z.enum(['manual', 'ai-mode']),
  totalQuestions: z
    .number()
    .min(1, 'At least 1 question required')
    .max(50, 'Maximum 50 questions allowed'),
  categoryConfigs: z
    .array(questionCategoryConfigSchema)
    .min(1, 'At least one category configuration is required'),
  questionTypes: z.array(z.string()).min(1, 'At least one question type is required'),
  questions: z.array(questionSchema).optional(), // Optional for both modes
});

// Interview config schema
export const interviewConfigSchema = z.object({
  duration: z
    .number()
    .min(5, 'Interview duration must be at least 5 minutes')
    .max(120, 'Interview duration cannot exceed 120 minutes'),
  instructions: z.string().optional(),
  difficultyLevel: z.enum(['easy', 'normal', 'hard', 'expert', 'advanced']).default('normal'),
  screenMonitoring: z.boolean().default(false),
  screenMonitoringMode: z.enum(['photo', 'video']).default('photo'),
  screenMonitoringInterval: z
    .number()
    .refine((val) => val === 30 || val === 60, {
      message: 'Interval must be 30 or 60 seconds',
    })
    .default(30)
    .optional(),
  cameraMonitoring: z.boolean().default(false),
  cameraMonitoringMode: z.enum(['photo', 'video']).default('photo'),
  cameraMonitoringInterval: z
    .number()
    .refine((val) => val === 30 || val === 60, {
      message: 'Interval must be 30 or 60 seconds',
    })
    .default(30)
    .optional(),
});

// Create job schema
export const createJobSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(100, 'Title cannot be more than 100 characters'),
  description: z.string().min(1, 'Description is required'),
  organizationId: objectIdSchema,
  organizationName: z.string().optional(), // Keep for backward compatibility
  industry: z.string().min(1, 'Industry is required'),
  expiryDate: z.date().min(new Date(), 'Expiry date must be in the future'),
  location: z.string().min(1, 'Location is required'),
  salary: z.string().optional(),
  currency: z.string().optional(),
  skills: z.array(z.string()).min(1, 'At least one skill is required'),
  requirements: z.string().optional(),
  benefits: z.string().optional(),
  status: z.enum(['draft', 'published', 'expired', 'deleted']).default('draft'),
  interviewConfig: interviewConfigSchema,
  questionsConfig: questionsConfigSchema,
  deletedAt: z.date().optional(),
  deletedBy: z.string().optional(),
});

// Update job schema
export const updateJobSchema = createJobSchema.partial().extend({
  id: z.string().min(1, 'Job ID is required'),
});

// Job query schema
export const jobQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  skills: z.array(z.string()).optional(),
  location: z.string().optional(),
  organizationName: z.string().optional(),
  organizationId: z.string().optional(),
  industry: z.string().optional(),
  isActive: z.boolean().optional(),
  sortBy: z
    .enum([
      'title',
      'organizationName',
      'industry',
      'location',
      'expiryDate',
      'createdAt',
      'updatedAt',
    ])
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Get jobs by recruiter schema
export const getJobsByRecruiterSchema = z.object({
  recruiterId: z.string().optional(), // If not provided, use current user
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  isActive: z.boolean().optional(),
  sortBy: z
    .enum(['title', 'companyName', 'location', 'expiryDate', 'createdAt', 'updatedAt'])
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Get job by ID schema
export const getJobByIdSchema = z.object({
  id: z.string().min(1, 'Job ID is required'),
});

// Get job stats schema
export const getJobStatsSchema = z.object({
  recruiterId: z.string().optional(), // If not provided, use current user
});

// Delete job schema
export const deleteJobSchema = z.string().min(1, 'Job ID is required');

// Form validation schema for job creation/editing
export const jobFormSchema = z.object({
  // Step 1: Basic Details
  title: z
    .string()
    .min(1, 'Job title is required')
    .max(100, 'Title cannot be more than 100 characters'),
  organizationId: objectIdSchema,
  industry: z.string().min(1, 'Industry is required'),
  salary: z.string().optional(),
  currency: z.string().optional(),
  location: z.string().min(1, 'Location is required'),
  expiryDate: z.date({
    required_error: 'Expiry date is required',
  }),
  skills: z.array(z.string()).min(1, 'At least one skill is required'),
  status: z.enum(['draft', 'published', 'expired', 'deleted']),

  // Step 2: Description & Details
  description: z.string().min(1, 'Job description is required'),
  requirements: z.string().optional(),
  benefits: z.string().optional(),

  // Step 3: Interview Configuration
  interviewConfig: interviewConfigSchema,

  // Step 4: Questions Configuration
  questionsConfig: questionsConfigSchema,
});

// Type exports for TypeScript inference
export type QuestionInput = z.infer<typeof questionSchema>;
export type QuestionCategoryConfigInput = z.infer<typeof questionCategoryConfigSchema>;
export type QuestionsConfigInput = z.infer<typeof questionsConfigSchema>;
export type InterviewConfigInput = z.infer<typeof interviewConfigSchema>;
export type CreateJobInput = z.infer<typeof createJobSchema>;
export type UpdateJobInput = z.infer<typeof updateJobSchema>;
export type JobQueryInput = z.infer<typeof jobQuerySchema>;
export type GetJobsByRecruiterInput = z.infer<typeof getJobsByRecruiterSchema>;
export type GetJobByIdInput = z.infer<typeof getJobByIdSchema>;
export type GetJobStatsInput = z.infer<typeof getJobStatsSchema>;
export type DeleteJobInput = z.infer<typeof deleteJobSchema>;
export type JobFormData = z.infer<typeof jobFormSchema>;

// Type alias for form components
export type FormValues = JobFormData;

// Re-export Organization type for job components
export type { Organization } from './organization';

// Component-specific interfaces
export interface JobFormModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  jobId?: string;
  mode: 'create' | 'edit';
}

export interface Step {
  id: number;
  title: string;
  description: string;
}

export interface Question {
  id: string;
  type: string;
  question: string;
  isAIGenerated?: boolean;
}

export interface QuestionCategoryConfig {
  type: string;
  numberOfQuestions: number;
}

export interface QuestionType {
  value: string;
  label: string;
  description: string;
}

export interface QuestionMode {
  value: 'manual' | 'ai-mode';
  label: string;
  description: string;
}

export interface Currency {
  value: string;
  label: string;
  symbol: string;
}

export interface JobStatus {
  value: 'draft' | 'published' | 'expired';
  label: string;
  description: string;
}

// Legacy questionsConfig type for migration
export interface LegacyQuestionsConfig {
  mode?: 'manual' | 'ai-mode';
  numberOfQuestions?: number;
  totalQuestions?: number;
  questionTypes?: string[];
  categoryConfigs?: QuestionCategoryConfig[];
  questions?: Question[];
}

// Type alias for Job
export type Job = IJob;

// Component-friendly Job type with string IDs instead of ObjectId
export interface JobForComponents extends Omit<IJob, 'organizationId' | 'recruiter'> {
  organizationId: string;
  recruiter: string;
}

// Pagination types
export interface PaginatedJobs {
  data: Job[];
  pagination: PaginationMeta;
}

// Filter and sort types
export interface JobFilters {
  page?: number;
  limit?: number;
  search?: string;
  skills?: string[];
  location?: string;
  organizationName?: string;
  organizationId?: string;
  industry?: string;
  isActive?: boolean;
  sortBy?:
    | 'title'
    | 'organizationName'
    | 'industry'
    | 'location'
    | 'expiryDate'
    | 'createdAt'
    | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export type JobSortField =
  | 'title'
  | 'organizationName'
  | 'industry'
  | 'location'
  | 'expiryDate'
  | 'createdAt'
  | 'updatedAt';
