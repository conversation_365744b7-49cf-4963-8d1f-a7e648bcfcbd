import { TRPCError } from '@trpc/server';
import passwordGenerator from 'generate-password';
import { z } from 'zod';

import { auth } from '@/auth';
import { connectToDatabase } from '@/db';
import User from '@/db/schema/user';

import { createTRPCRouter, publicProcedure } from '../trpc';

export const authRouter = createTRPCRouter({
  createUser: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        name: z.string().min(2).max(100),
      })
    )
    .mutation(async ({ input }) => {
      const { email, name } = input;

      try {
        await connectToDatabase();
        const isUserExists = await User.exists({ role: 'user', email: email });
        if (isUserExists) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'User with this email already exists. Please try logging in instead.',
          });
        }
        // Generate a random password
        const password = passwordGenerator.generate({
          length: 10,
          numbers: true,
        });

        const createUserResponse = await auth.api.createUser({
          body: {
            email,
            name,
            password,
            role: 'user',
          },
        });

        console.log('User created successfully:', createUserResponse);

        await auth.api.sendVerificationOTP({
          body: {
            email,
            type: 'sign-in',
          },
        });

        return createUserResponse;
      } catch (error) {
        if (error instanceof Error) {
          if (
            error.message.includes('User already exists') ||
            error.message.includes('already exists') ||
            error.message.includes('duplicate')
          ) {
            throw new TRPCError({
              code: 'CONFLICT',
              message: 'User with this email already exists. Please try logging in instead.',
            });
          }
        }

        // For other errors, throw a generic error
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create user. Please try again later.',
          cause: error,
        });
      }
    }),
  sendUserEmailOtp: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      const { email } = input;

      try {
        await connectToDatabase();

        const isUserExists = await User.exists({ role: 'user', email: email });

        if (!isUserExists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found. Please sign up first.',
          });
        }

        await auth.api.sendVerificationOTP({
          body: {
            email,
            type: 'sign-in',
          },
        });
        console.log('Sending OTP to email:', email);
        return { success: true };
      } catch (error) {
        console.error('Error in sendUserEmailOtp:', error);

        // Handle timeout specifically
        if (error instanceof Error && error.message.includes('timeout')) {
          throw new TRPCError({
            code: 'TIMEOUT',
            message: 'Database operation timed out. Please try again.',
          });
        }

        // Handle other database errors
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send OTP. Please try again later.',
          cause: error,
        });
      }
    }),
});
