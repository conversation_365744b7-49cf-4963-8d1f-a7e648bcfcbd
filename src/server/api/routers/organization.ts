import { TRPCError } from '@trpc/server';

import {
  createOrganizationSchema,
  getAllOrganizationsSchema,
  getOrganizationByIdSchema,
  getOrganizationBySlugSchema,
  getOrganizationJobsSchema,
  getOrganizationMembersSchema,
  updateOrganizationSchema,
} from '@/@types/organization';
import Member from '@/db/schema/member';
import Organization from '@/db/schema/organization';
import User from '@/db/schema/user';

import { createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';

export const organizationRouter = createTRPCRouter({
  createOrganization: protectedProcedure
    .input(createOrganizationSchema)
    .mutation(async ({ input }) => {
      const { name, slug, description, logo, metadata } = input;
      // Check if the organization already exists
      const existsOrg = await Organization.exists({ slug });
      if (existsOrg) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `Organization with slug '${slug}' already exists.`,
        });
      }
      // Create the new organization
      const newOrg = new Organization({
        name,
        slug,
        description: description || '',
        logo: logo || '',
        metadata: metadata || '',
      });
      await newOrg.save();
      return newOrg.toObject();
    }),

  getAllOrganizations: protectedProcedure
    .input(getAllOrganizationsSchema)
    .query(async ({ input }) => {
      const { page, limit, search, sortBy, sortOrder } = input;

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      // Build search query
      const searchQuery: Record<string, unknown> = {};
      if (search) {
        searchQuery.$or = [
          { name: { $regex: search, $options: 'i' } },
          { slug: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
        ];
      }

      // Build sort object
      const sortObject: Record<string, 1 | -1> = {};
      sortObject[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute queries in parallel for better performance
      const [organizations, totalCount] = await Promise.all([
        Organization.find(searchQuery).sort(sortObject).skip(skip).limit(limit),
        Organization.countDocuments(searchQuery),
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return {
        data: organizations.map((org) => org.toObject()),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage,
          hasPreviousPage,
        },
      };
    }),

  getOrganizationBySlug: protectedProcedure
    .input(getOrganizationBySlugSchema)
    .query(async ({ input: slug }) => {
      const organization = await Organization.findOne({
        slug,
      });
      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Organization with slug '${slug}' not found.`,
        });
      }
      return organization.toObject();
    }),

  getPublicOrganizationDetailsBySlug: publicProcedure
    .input(getOrganizationBySlugSchema)
    .query(async ({ input: slug }) => {
      const organization = await Organization.findOne({
        slug,
      });

      return organization?.toObject();
    }),

  getOrganizationById: protectedProcedure
    .input(getOrganizationByIdSchema)
    .query(async ({ input: id }) => {
      const organization = await Organization.findById(id);
      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Organization with ID '${id}' not found.`,
        });
      }
      return organization.toObject();
    }),

  updateOrganization: protectedProcedure
    .input(updateOrganizationSchema)
    .mutation(async ({ input }) => {
      const { id, name, slug, description, logo, metadata } = input;

      // Find the organization by ID
      const organization = await Organization.findById(id);
      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Organization with ID '${id}' not found.`,
        });
      }
      // Check if the slug is being updated and if it already exists
      if (slug && slug !== organization.slug) {
        const existsOrg = await Organization.exists({ slug });
        if (existsOrg) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: `Organization with slug '${slug}' already exists.`,
          });
        }
      }
      // Update the organization fields
      organization.name = name || organization.name;
      organization.slug = slug || organization.slug;
      organization.description = description || organization.description;
      organization.logo = logo || organization.logo;
      organization.metadata = metadata || organization.metadata;
      await organization.save();
      return organization.toObject();
    }),

  // Get organization members
  getOrganizationMembers: protectedProcedure
    .input(getOrganizationMembersSchema)
    .query(async ({ input: organizationId }) => {
      // Find all members for the organization
      const members = await Member.find({ organizationId }).lean();

      // Get user details for each member
      const memberDetails = await Promise.all(
        members.map(async (member) => {
          const user = await User.findById(member.userId).lean();
          if (!user) {
            return null;
          }
          return {
            id: member._id.toString(),
            userId: user._id.toString(),
            name: user.name,
            email: user.email,
            image: user.image || '',
            role: member.role,
            joinedAt: member.createdAt,
          };
        })
      );

      // Filter out null values (users that couldn't be found)
      return memberDetails.filter((member) => member !== null);
    }),

  // Get organization jobs
  getOrganizationJobs: protectedProcedure
    .input(getOrganizationJobsSchema)
    .query(async ({ input: _organizationId }) => {
      // For now, return mock data since there's no job schema in the current setup
      // In a real implementation, you would query jobs based on organizationId
      const mockJobs = [
        {
          id: '1',
          title: 'Senior Frontend Developer',
          department: 'Engineering',
          location: 'Remote',
          type: 'Full-time',
          status: 'Active',
          applicants: 25,
          postedAt: new Date('2024-05-20'),
        },
        {
          id: '2',
          title: 'Product Manager',
          department: 'Product',
          location: 'San Francisco, CA',
          type: 'Full-time',
          status: 'Active',
          applicants: 18,
          postedAt: new Date('2024-06-01'),
        },
        {
          id: '3',
          title: 'UX Designer',
          department: 'Design',
          location: 'New York, NY',
          type: 'Contract',
          status: 'Draft',
          applicants: 0,
          postedAt: new Date('2024-06-10'),
        },
      ];
      return mockJobs;
    }),
});
