import { TRPCError } from '@trpc/server';

import {
  createRecruiterSchema,
  deleteR<PERSON>ruiterSchema,
  getAllRecruitersSchema,
  getRecruiterByEmailSchema,
  getRecruiterByIdSchema,
  updateRecruiterSchema,
} from '@/@types/recruiter';
import Member from '@/db/schema/member';
import User from '@/db/schema/user';
import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc';

export const recruiterRouter = createTRPCRouter({
  createRecruiter: protectedProcedure.input(createRecruiterSchema).mutation(async ({ input }) => {
    const { name, email, emailVerified, image } = input;

    // Check if user already exists with this email
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'A user with this email already exists',
      });
    }

    // Create new recruiter (user with role 'recruiter')
    const newRecruiter = new User({
      name,
      email,
      emailVerified: emailVerified ?? false,
      image: image || '',
      role: 'recruiter', // Recruiters have role 'recruiter'
    });

    await newRecruiter.save();

    return newRecruiter.toObject();
  }),

  getAllRecruiters: protectedProcedure.input(getAllRecruitersSchema).query(async ({ input }) => {
    const { page, limit, search, organizationId, sortBy, sortOrder } = input;

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // If organizationId is provided, first get the user IDs of members in that organization
    let userIdsInOrganization: string[] | undefined;
    if (organizationId) {
      const members = await Member.find({ organizationId }).lean();
      userIdsInOrganization = members.map((member) => member.userId);

      // If no members found, return empty result
      if (userIdsInOrganization.length === 0) {
        return {
          data: [],
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        };
      }
    }

    // Build search query - only get users with role 'recruiter'
    const searchQuery: Record<string, unknown> = { role: 'recruiter' };

    // Filter by organization membership if specified
    if (userIdsInOrganization) {
      searchQuery._id = { $in: userIdsInOrganization };
    }

    if (search) {
      searchQuery.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    // Build sort object
    const sortObject: Record<string, 1 | -1> = {};
    sortObject[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute queries in parallel for better performance
    const [recruiters, totalCount] = await Promise.all([
      User.find(searchQuery).sort(sortObject).skip(skip).limit(limit),
      User.countDocuments(searchQuery),
    ]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      data: recruiters.map((recruiter) => recruiter.toObject()),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    };
  }),

  getRecruiterById: protectedProcedure
    .input(getRecruiterByIdSchema)
    .query(async ({ input: id }) => {
      const recruiter = await User.findOne({ _id: id, role: 'recruiter' });
      if (!recruiter) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Recruiter not found',
        });
      }

      return recruiter.toObject();
    }),

  getRecruiterByEmail: protectedProcedure
    .input(getRecruiterByEmailSchema)
    .query(async ({ input: email }) => {
      const recruiter = await User.findOne({ email, role: 'recruiter' });
      if (!recruiter) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Recruiter not found',
        });
      }

      return recruiter.toObject();
    }),

  updateRecruiter: protectedProcedure.input(updateRecruiterSchema).mutation(async ({ input }) => {
    const { id, name, email, emailVerified, image } = input;

    // Find the recruiter by ID and ensure it's a recruiter role
    const recruiter = await User.findOne({ _id: id, role: 'recruiter' });
    if (!recruiter) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Recruiter not found',
      });
    }

    // Check if email is being updated and if it already exists
    if (email && email !== recruiter.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'A user with this email already exists',
        });
      }
    }

    // Update the recruiter fields
    recruiter.name = name || recruiter.name;
    recruiter.email = email || recruiter.email;
    recruiter.emailVerified = emailVerified ?? recruiter.emailVerified;
    recruiter.image = image || recruiter.image;

    await recruiter.save();

    return recruiter.toObject();
  }),

  deleteRecruiter: protectedProcedure
    .input(deleteRecruiterSchema)
    .mutation(async ({ input: id }) => {
      const recruiter = await User.findOne({ _id: id, role: 'recruiter' });
      if (!recruiter) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Recruiter not found',
        });
      }

      await User.findByIdAndDelete(id);

      return {
        success: true,
        message: 'Recruiter deleted successfully',
      };
    }),
});
