import { TRPCError } from '@trpc/server';
import mongoose from 'mongoose';

import {
  createJobSchema,
  getJobByIdSchema,
  getJobsByRecruiterSchema,
  getJobStatsSchema,
  jobQuerySchema,
  updateJobSchema,
} from '@/@types/job';
import Job from '@/db/schema/job';
import { createTRPCRouter, protectedProcedure, publicProcedure } from '@/server/api/trpc';

export const jobRouter = createTRPCRouter({
  // Create a new job (protected - only authenticated users)
  create: protectedProcedure.input(createJobSchema).mutation(async ({ input, ctx }) => {
    const userId = ctx.session.user.id;

    console.log('API received input:', input); // Debug logging

    // Create new job
    const newJob = new Job({
      ...input,
      recruiter: new mongoose.Types.ObjectId(userId),
    });

    console.log('Creating job with data:', newJob.toObject()); // Debug logging

    await newJob.save();

    // Populate recruiter info and return
    await newJob.populate('recruiter', 'name email');

    return newJob.toObject();
  }),

  // Get all jobs with filtering and pagination (public)
  getAll: publicProcedure.input(jobQuerySchema).query(async ({ input }) => {
    const {
      page,
      limit,
      search,
      skills,
      location,
      organizationName,
      industry,
      isActive,
      sortBy,
      sortOrder,
    } = input;

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Build search query
    const searchQuery: Record<string, unknown> = {};

    // Filter by active status if specified
    if (isActive !== undefined) {
      searchQuery.isActive = isActive;
    }

    // Text search across title, description, organization name, and industry
    if (search) {
      searchQuery.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { organizationName: { $regex: search, $options: 'i' } },
        { industry: { $regex: search, $options: 'i' } },
      ];
    }

    // Filter by skills
    if (skills && skills.length > 0) {
      searchQuery.skills = { $in: skills };
    }

    // Filter by location
    if (location) {
      searchQuery.location = { $regex: location, $options: 'i' };
    }

    // Filter by organization name
    if (organizationName) {
      searchQuery.organizationName = { $regex: organizationName, $options: 'i' };
    }

    // Filter by industry
    if (industry) {
      searchQuery.industry = { $regex: industry, $options: 'i' };
    }

    // Build sort object
    const sortObject: Record<string, 1 | -1> = {};
    sortObject[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute queries in parallel for better performance
    const [jobs, totalCount] = await Promise.all([
      Job.find(searchQuery)
        .populate('recruiter', 'name email')
        .sort(sortObject)
        .skip(skip)
        .limit(limit),
      Job.countDocuments(searchQuery),
    ]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      data: jobs.map((job) => job.toObject()),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    };
  }),

  // Get jobs by recruiter (protected)
  getByRecruiter: protectedProcedure
    .input(getJobsByRecruiterSchema)
    .query(async ({ input, ctx }) => {
      const { recruiterId, page, limit, isActive, sortBy, sortOrder } = input;
      const userId = recruiterId || ctx.session.user.id;

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      // Build search query
      const searchQuery: Record<string, unknown> = {
        recruiter: new mongoose.Types.ObjectId(userId),
      };

      if (isActive !== undefined) {
        searchQuery.isActive = isActive;
      }

      // Build sort object
      const sortObject: Record<string, 1 | -1> = {};
      sortObject[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute queries in parallel
      const [jobs, totalCount] = await Promise.all([
        Job.find(searchQuery)
          .populate('recruiter', 'name email')
          .sort(sortObject)
          .skip(skip)
          .limit(limit),
        Job.countDocuments(searchQuery),
      ]);

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: jobs.map((job) => job.toObject()),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    }),

  // Get job by ID (public)
  getById: publicProcedure.input(getJobByIdSchema).query(async ({ input }) => {
    const { id } = input;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Invalid job ID format',
      });
    }

    const job = await Job.findById(id).populate('recruiter', 'name email');

    if (!job) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Job not found',
      });
    }

    // Ensure consistent transformation for API response
    return job.toObject();
  }),

  // Update job (protected - only job owner)
  update: protectedProcedure.input(updateJobSchema).mutation(async ({ input, ctx }) => {
    const { id, ...updateData } = input;
    const userId = ctx.session.user.id;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Invalid job ID format',
      });
    }

    // Find the job and check ownership
    const existingJob = await Job.findById(id);
    if (!existingJob) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Job not found',
      });
    }

    if (existingJob.recruiter.toString() !== userId) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You can only update your own jobs',
      });
    }

    // Update the job
    const updatedJob = await Job.findByIdAndUpdate(id, updateData, {
      new: true,
      runValidators: true,
    }).populate('recruiter', 'name email');

    if (!updatedJob) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to update job',
      });
    }

    // Ensure consistent transformation for API response
    return updatedJob.toObject();
  }),

  // Get job statistics (protected)
  getStats: protectedProcedure.input(getJobStatsSchema).query(async ({ input, ctx }) => {
    const userId = input.recruiterId || ctx.session.user.id;

    const stats = await Job.aggregate([
      { $match: { recruiter: new mongoose.Types.ObjectId(userId) } },
      {
        $group: {
          _id: null,
          totalJobs: { $sum: 1 },
          activeJobs: { $sum: { $cond: ['$isActive', 1, 0] } },
          inactiveJobs: { $sum: { $cond: ['$isActive', 0, 1] } },
          expiredJobs: {
            $sum: { $cond: [{ $lt: ['$expiryDate', new Date()] }, 1, 0] },
          },
        },
      },
    ]);

    const result = stats[0] || {
      totalJobs: 0,
      activeJobs: 0,
      inactiveJobs: 0,
      expiredJobs: 0,
    };

    return {
      totalJobs: result.totalJobs,
      activeJobs: result.activeJobs,
      inactiveJobs: result.inactiveJobs,
      expiredJobs: result.expiredJobs,
    };
  }),
});
